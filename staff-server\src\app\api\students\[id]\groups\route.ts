/**
 * Student group management endpoints
 * Handles enrollment and removal of students from groups
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  validateRequiredFields,
  isValidUUID
} from '@/lib/utils';
import { logStudentOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/shared/types/common';

interface StudentGroup {
  id: string;
  student_id: string;
  group_id: string;
  enrollment_date: Date;
  status: string;
  created_at: Date;
  updated_at: Date;
}

interface GroupWithDetails {
  id: string;
  group_id: string;
  name: string;
  level?: string;
  teacher_name?: string;
  enrollment_date: Date;
  status: string;
}

// GET /api/students/[id]/groups - Get student's groups
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid student ID format', 400);
    }

    // Check permissions - all staff roles can view student groups
    if (!['management', 'reception', 'teacher'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      // Check if student exists
      const studentResult = await query(
        'SELECT id FROM students WHERE id = $1',
        [id]
      );

      if (studentResult.rows.length === 0) {
        return createErrorResponse('Student not found', 404);
      }

      // Get student's groups with details
      const groupsResult = await query<GroupWithDetails>(
        `SELECT 
           sg.id, g.id as group_id, g.name, g.level, u.name as teacher_name,
           sg.enrollment_date, sg.status
         FROM student_groups sg
         JOIN groups g ON sg.group_id = g.id
         LEFT JOIN users u ON g.teacher_id = u.id
         WHERE sg.student_id = $1
         ORDER BY sg.enrollment_date DESC`,
        [id]
      );

      return createResponse({
        studentId: id,
        groups: groupsResult.rows.map(group => ({
          id: group.id,
          groupId: group.group_id,
          name: group.name,
          level: group.level,
          teacherName: group.teacher_name,
          enrollmentDate: group.enrollment_date,
          status: group.status
        }))
      }, true, 'Student groups retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving student groups:', dbError);
      return createErrorResponse('Failed to retrieve student groups', 500);
    }

  } catch (error) {
    console.error('Get student groups error:', error);
    return createErrorResponse('Failed to retrieve student groups', 500);
  }
}

// POST /api/students/[id]/groups - Enroll student in a group
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid student ID format', 400);
    }

    // Check permissions - management and reception can enroll students
    if (!['management', 'reception'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { groupId, enrollmentDate = new Date().toISOString().split('T')[0] } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['groupId']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    // Validate groupId format
    if (!isValidUUID(groupId)) {
      return createErrorResponse('Invalid group ID format', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Check if student exists
      const studentResult = await query(
        'SELECT id, first_name, last_name FROM students WHERE id = $1',
        [id]
      );

      if (studentResult.rows.length === 0) {
        return createErrorResponse('Student not found', 404);
      }

      const student = studentResult.rows[0];

      // Check if group exists and has capacity
      const groupResult = await query(
        `SELECT g.id, g.name, g.max_students, g.is_active,
                COUNT(sg.id) as current_students
         FROM groups g
         LEFT JOIN student_groups sg ON g.id = sg.group_id AND sg.status = 'active'
         WHERE g.id = $1 AND g.is_active = true
         GROUP BY g.id, g.name, g.max_students, g.is_active`,
        [groupId]
      );

      if (groupResult.rows.length === 0) {
        return createErrorResponse('Group not found or inactive', 404);
      }

      const group = groupResult.rows[0];
      const currentStudents = parseInt(group.current_students);

      if (currentStudents >= group.max_students) {
        return createErrorResponse('Group is at maximum capacity', 400);
      }

      // Check if student is already enrolled in this group
      const existingEnrollmentResult = await query(
        'SELECT id FROM student_groups WHERE student_id = $1 AND group_id = $2',
        [id, groupId]
      );

      if (existingEnrollmentResult.rows.length > 0) {
        return createErrorResponse('Student is already enrolled in this group', 409);
      }

      // Enroll student in group
      const enrollmentResult = await query<StudentGroup>(
        `INSERT INTO student_groups (student_id, group_id, enrollment_date, status)
         VALUES ($1, $2, $3, 'active')
         RETURNING id, student_id, group_id, enrollment_date, status, created_at, updated_at`,
        [id, groupId, enrollmentDate]
      );

      const enrollment = enrollmentResult.rows[0];

      // Log student enrollment
      await logStudentOperation(
        'UPDATE' as any,
        authResult.user.id,
        {
          id: student.id,
          first_name: student.first_name,
          last_name: student.last_name,
          groupEnrollment: {
            groupId: group.id,
            groupName: group.name,
            enrollmentDate
          }
        },
        undefined,
        context
      );

      return createResponse({
        id: enrollment.id,
        studentId: enrollment.student_id,
        groupId: enrollment.group_id,
        groupName: group.name,
        enrollmentDate: enrollment.enrollment_date,
        status: enrollment.status,
        createdAt: enrollment.created_at
      }, true, 'Student enrolled in group successfully', undefined, 201);

    } catch (dbError) {
      console.error('Database error enrolling student in group:', dbError);
      return createErrorResponse('Failed to enroll student in group', 500);
    }

  } catch (error) {
    console.error('Enroll student in group error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}
