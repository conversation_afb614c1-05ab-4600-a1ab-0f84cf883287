/**
 * Staff server activity synchronization endpoint
 * Receives activity logs from staff server for centralized audit trail
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';

// Validate API key for inter-service communication
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const expectedKey = process.env.STAFF_SERVER_API_KEY;
  
  if (!expectedKey) {
    console.warn('STAFF_SERVER_API_KEY not configured');
    return false;
  }
  
  return apiKey === expectedKey;
}

export async function POST(request: NextRequest) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return createErrorResponse('Invalid API key', 401);
    }

    // Validate source service
    const sourceService = request.headers.get('X-Source-Service');
    if (sourceService !== 'staff-server') {
      return createErrorResponse('Invalid source service', 400);
    }

    const body = await request.json();
    const {
      userId,
      action,
      resourceType,
      resourceId,
      oldValues,
      newValues,
      description,
      ipAddress,
      userAgent,
      timestamp
    } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['userId', 'action', 'resourceType']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    try {
      // Insert activity log from staff server
      const sql = `
        INSERT INTO activity_logs (
          user_id, action, resource_type, resource_id,
          old_values, new_values, ip_address, user_agent, description,
          timestamp
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING id
      `;

      // Prefix description with [Staff Server] to indicate source
      const prefixedDescription = description ? `[Staff Server] ${description}` : '[Staff Server] Activity from staff server';

      const params = [
        userId,
        action,
        resourceType,
        resourceId || null,
        oldValues ? JSON.stringify(oldValues) : null,
        newValues ? JSON.stringify(newValues) : null,
        ipAddress || null,
        userAgent || null,
        prefixedDescription,
        timestamp || new Date().toISOString()
      ];
      
      const result = await query(sql, params);
      const logId = result.rows[0].id;

      return createResponse({
        id: logId,
        synced: true,
        timestamp: new Date().toISOString()
      }, true, 'Activity log synchronized successfully');

    } catch (dbError) {
      console.error('Database error syncing staff activity log:', dbError);
      return createErrorResponse('Failed to sync activity log', 500);
    }

  } catch (error) {
    console.error('Staff activity sync error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}
